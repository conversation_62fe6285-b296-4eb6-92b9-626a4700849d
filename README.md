# Pharmaciaty Products MCP Server

A production-grade Model Context Protocol (MCP) server that exposes the Pharmaciaty Products API to AI assistants like <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and VS Code extensions.

## 🚀 Features

- **🤖 Multi-Platform Support**: Works with Claude Desktop, VS Code, ChatGPT, and any MCP-compatible client
- **🔍 Comprehensive Product Search**: Search by name, brand, category, or ingredients
- **📦 Inventory Management**: Real-time stock checking across warehouses
- **🏗️ Production-Ready**: Robust error handling, logging, and monitoring
- **🔧 Flexible Deployment**: Run as MCP server or HTTP API
- **📚 Full Documentation**: Complete API reference and examples

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Reference](#api-reference)
- [Deployment](#deployment)
- [Development](#development)
- [Troubleshooting](#troubleshooting)

## ⚡ Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

```bash
cp .env.example .env
# Edit .env with your Pharmaciaty API token
```

### 3. Run the Server

**For Claude Desktop / VS Code:**
```bash
python main.py --mode mcp
```

**For ChatGPT / HTTP clients:**
```bash
python main.py --mode chatgpt --host 0.0.0.0 --port 8000
```

## 🛠️ Installation

### Prerequisites

- Python 3.8+
- Pharmaciaty API access token
- (Optional) OpenAI API key for ChatGPT integration

### Install from Source

```bash
git clone https://github.com/your-org/pharmaciaty-mcp
cd pharmaciaty-mcp
pip install -r requirements.txt
```

### Docker Installation

```bash
docker build -t pharmaciaty-mcp .
docker run -p 8000:8000 --env-file .env pharmaciaty-mcp
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Required
PHARMACIATY_API_TOKEN=your_api_token_here

# Optional
PHARMACIATY_API_BASE_URL=https://api.pharmaciaty.cloud/api/v1
PHARMACIATY_SERVER_NAME=Pharmaciaty Products API
PHARMACIATY_LOG_LEVEL=INFO
PHARMACIATY_OPENAI_API_KEY=your_openai_key_here
```

### Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `PHARMACIATY_API_TOKEN` | API authentication token | Required |
| `PHARMACIATY_API_BASE_URL` | Base URL for API | `https://api.pharmaciaty.cloud/api/v1` |
| `PHARMACIATY_API_TIMEOUT` | Request timeout (seconds) | `30` |
| `PHARMACIATY_LOG_LEVEL` | Logging level | `INFO` |
| `PHARMACIATY_OPENAI_API_KEY` | OpenAI API key for ChatGPT | Optional |

## 🎯 Usage

### Claude Desktop Integration

1. **Install the server:**
   ```bash
   python main.py --mode mcp
   ```

2. **Add to Claude Desktop config** (`~/Library/Application Support/Claude/claude_desktop_config.json`):
   ```json
   {
     "mcpServers": {
       "pharmaciaty-products": {
         "command": "python",
         "args": ["/path/to/pharmaciaty-mcp/main.py", "--mode", "mcp"],
         "env": {
           "PHARMACIATY_API_TOKEN": "your_token_here"
         }
       }
     }
   }
   ```

3. **Restart Claude Desktop**

### ChatGPT Integration

1. **Start HTTP server:**
   ```bash
   python main.py --mode chatgpt --port 8000
   ```

2. **Access via HTTP:**
   - API Documentation: `http://localhost:8000/docs`
   - Health Check: `http://localhost:8000/health`
   - OpenAPI Spec: `http://localhost:8000/openapi.json`

### VS Code Integration

1. **Install MCP extension** in VS Code
2. **Configure the server** in VS Code settings
3. **Use the tools** in your AI assistant

## 📖 API Reference

### Available Tools

#### `search_products`
Search for products in the catalog.

**Parameters:**
- `search` (string): Search term (product name, brand, ingredient)
- `limit` (integer): Number of results (1-100, default: 20)
- `category` (string, optional): Product category filter
- `is_available` (boolean): Filter available products (default: true)
- `warehouses` (string, optional): Comma-separated warehouse IDs

**Example:**
```python
# Search for pain medication
result = await search_products(search="paracetamol", limit=10)
```

#### `get_product_details`
Get detailed information about a specific product.

**Parameters:**
- `product_id` (string): Unique product identifier

**Example:**
```python
# Get product details
product = await get_product_details(product_id="12345")
```

#### `check_product_inventory`
Check inventory status across warehouses.

**Parameters:**
- `product_id` (string): Unique product identifier

**Example:**
```python
# Check inventory
inventory = await check_product_inventory(product_id="12345")
```

#### `search_products_by_category`
Search products within a specific category.

**Parameters:**
- `category` (string): Product category
- `page` (integer): Page number (default: 1)
- `limit` (integer): Results per page (1-100, default: 20)
- `is_available` (boolean): Filter available products (default: true)

### Available Resources

#### `products://search/{query}`
Provides search results as a readable resource.

#### `products://details/{product_id}`
Provides detailed product information as a resource.

#### `server://info`
Server information and capabilities.

#### `server://health`
Health check and API connectivity status.

## 🚀 Deployment

### Local Development

```bash
# MCP mode (Claude Desktop)
python main.py --mode mcp

# HTTP mode (ChatGPT)
python main.py --mode chatgpt --host 0.0.0.0 --port 8000
```

### Docker Deployment

```bash
# Build image
docker build -t pharmaciaty-mcp .

# Run container
docker run -d \
  --name pharmaciaty-mcp \
  -p 8000:8000 \
  --env-file .env \
  pharmaciaty-mcp
```

### Google Cloud Run

```bash
# Deploy to Cloud Run
gcloud run deploy pharmaciaty-mcp \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars PHARMACIATY_API_TOKEN=your_token
```

## 🔧 Development

### Project Structure

```
pharmaciaty-mcp/
├── src/pharmaciaty_mcp/          # Main package
│   ├── api/                      # API client
│   ├── server/                   # MCP server
│   ├── integrations/             # Platform integrations
│   └── utils/                    # Utilities
├── tests/                        # Test suite
├── docs/                         # Documentation
├── examples/                     # Usage examples
├── main.py                       # Entry point
└── requirements.txt              # Dependencies
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-httpx

# Run tests
pytest tests/

# Run with coverage
pytest --cov=src/pharmaciaty_mcp tests/
```

### Code Quality

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/
```

## 🐛 Troubleshooting

### Common Issues

**1. Authentication Error**
```
Error: Invalid API token
```
**Solution:** Verify your `PHARMACIATY_API_TOKEN` in `.env`

**2. Connection Error**
```
Error: Network error
```
**Solution:** Check internet connection and API endpoint

**3. Claude Desktop Not Recognizing**
```
Server not found
```
**Solution:** Restart Claude Desktop after configuration changes

### Debug Mode

Enable debug logging:
```bash
python main.py --mode mcp --log-level DEBUG
```

### Health Check

Test API connectivity:
```bash
curl http://localhost:8000/health
```

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-org/pharmaciaty-mcp/issues)
- **Documentation**: [Full Documentation](https://your-org.github.io/pharmaciaty-mcp)
- **Email**: <EMAIL>
