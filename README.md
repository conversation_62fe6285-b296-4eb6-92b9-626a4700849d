# Pharmaciaty Products MCP

## Overview
This Microservice Control Plane (MCP) exposes a single, secure, and unified endpoint for Pharmaciaty’s Product Catalogue. Built with FastAPI and [fastapi_mcp](https://github.com/Pharmaciaty/fastapi_mcp), it proxies requests to the upstream /products API, forwarding all query parameters and the Authorization header.

## Why Use this MCP?
- **Centralized Gateway:** Single entry point for product catalogue queries.
- **Security:** Control and monitor access to your catalogue.
- **Extensibility:** Easily add authentication, rate limiting, and caching in the future.

## Project Structure
```
.
├── main.py           # Main FastAPI MCP application
├── requirements.txt  # Python dependencies
└── README.md         # Project documentation
```

## Endpoints
- **/health**: Health check endpoint.
- **/products**: Proxies all query parameters and Authorization header to the upstream Pharmaciaty /products API.

## Local Development

### 1. Clone the repository
```bash
git clone <your-repo-url>
cd CatelogMCP
```

### 2. Set up a Python virtual environment
```bash
python3 -m venv venv
source venv/bin/activate
```

### 3. Install dependencies
```bash
pip install -r requirements.txt
```

### 4. Run the MCP locally
```bash
uvicorn main:app --reload
```

The API will be available at [http://localhost:8000](http://localhost:8000)

## Testing the MCP

### Using Swagger UI
Visit [http://localhost:8000/docs](http://localhost:8000/docs) for interactive API documentation.

### Using cURL
Example (replace <TOKEN> with your actual bearer token):
```bash
curl 'http://localhost:8000/products?sort_by=is_active&sort_order=asc&page=1&limit=100&search=CISTAL-1&warehouses=V2FyZWhvdXNlOjk1NjA5%2CV2FyZWhvdXNlOjk3MTQ4&is_available=true' \
  -H 'Authorization: bearer <TOKEN>'
```

## Deployment: Google Cloud Run

### 1. Build Docker image
Create a `Dockerfile` (example below):
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY . .
RUN pip install --no-cache-dir -r requirements.txt
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
```

Build and push the image:
```bash
gcloud builds submit --tag gcr.io/<your-project-id>/pharmaciaty-mcp
```

### 2. Deploy to Cloud Run
```bash
gcloud run deploy pharmaciaty-mcp \
  --image gcr.io/<your-project-id>/pharmaciaty-mcp \
  --platform managed \
  --region <your-region> \
  --allow-unauthenticated
```

## Extending the MCP
- **Authentication:** Add middleware or FastAPI dependencies for OAuth2/JWT.
- **Rate Limiting:** Integrate with libraries like [slowapi](https://pypi.org/project/slowapi/).
- **Caching:** Use [fastapi-cache2](https://pypi.org/project/fastapi-cache2/) for response caching.

## Contributing
- Follow PEP8 and ensure code is well-commented.
- Submit PRs with clear descriptions.

## License
MIT 