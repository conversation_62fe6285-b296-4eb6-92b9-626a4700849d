#!/usr/bin/env python3
"""
Test script to verify the Pharmaciaty API connection works
"""
import asyncio
import os
from dotenv import load_dotenv
from main import make_api_request

load_dotenv()

async def test_api():
    """Test the API connection"""
    print("Testing Pharmaciaty API connection...")
    
    try:
        # Test basic API connection without filters first
        result = await make_api_request("/products/", {
            "limit": 10,
            "page": 1
        })        
        print(f"✅ API connection successful!")
        print(f"📊 Found {result.get('pagination', {}).get('total', 0)} total products")
        print(f"📦 Retrieved {len(result.get('data', []))} products in this page")
        
        # Show first product if available
        products = result.get('data', [])
        if products:
            first_product = products[0]
            print(f"\n📋 Sample product:")
            print(f"   ID: {first_product.get('id', 'N/A')}")
            print(f"   Name: {first_product.get('name', 'N/A')}")
            print(f"   Brand: {first_product.get('brand', 'N/A')}")
            print(f"   Available: {first_product.get('is_active', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ API connection failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api())
    if success:
        print("\n🎉 Your MCP server is ready to use!")
        print("💡 You can now test it in the MCP Inspector or Claude Desktop")
    else:
        print("\n⚠️  Please check your API token and network connection")
