#!/usr/bin/env python3
"""
Usage examples for Pharmaciaty MCP Server.

This file demonstrates how to use the MCP server programmatically
and provides examples of common use cases.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pharmaciaty_mcp.api.client import PharmaciatyAPIClient
from pharmaciaty_mcp.config import config


async def example_product_search():
    """Example: Search for products."""
    print("🔍 Searching for products...")
    
    async with PharmaciatyAPIClient() as client:
        # Search for pain medication
        result = await client.search_products(
            search="paracetamol",
            limit=5,
            is_available=True
        )
        
        print(f"Found {result.total_count} products:")
        for product in result.products:
            print(f"  - {product.name} ({product.brand or 'No brand'})")
            if product.price:
                print(f"    Price: {product.price} {product.currency or ''}")


async def example_product_details():
    """Example: Get product details."""
    print("\n📋 Getting product details...")
    
    async with PharmaciatyAPIClient() as client:
        # First search for a product to get an ID
        search_result = await client.search_products(search="vitamin", limit=1)
        
        if search_result.products:
            product_id = search_result.products[0].id
            
            # Get detailed information
            product = await client.get_product_details(product_id)
            
            print(f"Product: {product.name}")
            print(f"Brand: {product.brand or 'N/A'}")
            print(f"Category: {product.category or 'N/A'}")
            print(f"Description: {product.description or 'N/A'}")
            print(f"Available: {'Yes' if product.is_available else 'No'}")


async def example_inventory_check():
    """Example: Check product inventory."""
    print("\n📦 Checking inventory...")
    
    async with PharmaciatyAPIClient() as client:
        # First search for a product
        search_result = await client.search_products(search="medicine", limit=1)
        
        if search_result.products:
            product_id = search_result.products[0].id
            
            try:
                # Check inventory
                inventory = await client.check_inventory(product_id)
                
                print(f"Inventory for {search_result.products[0].name}:")
                total_quantity = 0
                
                for item in inventory:
                    print(f"  - Warehouse: {item.warehouse_name or item.warehouse_id or 'Unknown'}")
                    print(f"    Quantity: {item.quantity}")
                    if item.location:
                        print(f"    Location: {item.location}")
                    total_quantity += item.quantity
                
                print(f"Total available: {total_quantity}")
                
            except Exception as e:
                print(f"Could not check inventory: {e}")


async def example_category_search():
    """Example: Search by category."""
    print("\n🏷️ Searching by category...")
    
    async with PharmaciatyAPIClient() as client:
        # Search for products in a specific category
        result = await client.search_products(
            category="Pain Management",
            limit=3,
            is_available=True
        )
        
        print(f"Found {result.total_count} products in Pain Management:")
        for product in result.products:
            print(f"  - {product.name}")
            if product.composition:
                print(f"    Composition: {product.composition}")


async def main():
    """Run all examples."""
    print("🚀 Pharmaciaty MCP Server Examples")
    print("=" * 50)
    
    try:
        await example_product_search()
        await example_product_details()
        await example_inventory_check()
        await example_category_search()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure you have:")
        print("1. Valid PHARMACIATY_API_TOKEN in .env")
        print("2. Internet connection")
        print("3. Access to Pharmaciaty API")


if __name__ == "__main__":
    asyncio.run(main())
