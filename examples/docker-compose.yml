version: '3.8'

services:
  pharmaciaty-mcp:
    build: .
    ports:
      - "8000:8000"
    environment:
      - PHARMACIATY_API_TOKEN=${PHARMACIATY_API_TOKEN}
      - PHARMACIATY_LOG_LEVEL=INFO
      - PHARMACIATY_OPENAI_API_KEY=${OPENAI_API_KEY}
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
