"""
Tests for the Pharmaciaty API client.
"""

import pytest
import httpx
from unittest.mock import AsyncMock, patch
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pharmaciaty_mcp.api.client import PharmaciatyAPIClient
from pharmaciaty_mcp.api.models import APIError, AuthenticationError, NotFoundError


@pytest.fixture
def api_client():
    """Create a test API client."""
    return PharmaciatyAPIClient(api_token="test_token")


@pytest.mark.asyncio
async def test_client_initialization():
    """Test API client initialization."""
    client = PharmaciatyAPIClient(api_token="test_token")
    assert client.api_token == "test_token"
    assert client.base_url == "https://api.pharmaciaty.cloud/api/v1"


@pytest.mark.asyncio
async def test_client_missing_token():
    """Test client initialization without token."""
    with patch.dict('os.environ', {}, clear=True):
        with pytest.raises(ValueError, match="API token is required"):
            PharmaciatyAPIClient()


@pytest.mark.asyncio
async def test_search_products_success(api_client):
    """Test successful product search."""
    mock_response = {
        "count": 2,
        "data": [
            {
                "id": "1",
                "name": "Test Product 1",
                "brand": "Test Brand",
                "is_available": True,
                "is_active": True
            },
            {
                "id": "2", 
                "name": "Test Product 2",
                "brand": "Test Brand 2",
                "is_available": True,
                "is_active": True
            }
        ]
    }
    
    with patch.object(api_client, '_make_request', return_value=mock_response):
        result = await api_client.search_products(search="test", limit=10)
        
        assert result.total_count == 2
        assert len(result.products) == 2
        assert result.products[0].name == "Test Product 1"
        assert result.search_query == "test"


@pytest.mark.asyncio
async def test_get_product_details_success(api_client):
    """Test successful product details retrieval."""
    mock_response = {
        "id": "123",
        "name": "Test Product",
        "brand": "Test Brand",
        "description": "Test description",
        "is_available": True,
        "is_active": True
    }
    
    with patch.object(api_client, '_make_request', return_value=mock_response):
        product = await api_client.get_product_details("123")
        
        assert product.id == "123"
        assert product.name == "Test Product"
        assert product.brand == "Test Brand"


@pytest.mark.asyncio
async def test_authentication_error(api_client):
    """Test authentication error handling."""
    with patch.object(api_client, '_make_request', side_effect=AuthenticationError("Invalid token")):
        with pytest.raises(AuthenticationError):
            await api_client.search_products()


@pytest.mark.asyncio
async def test_not_found_error(api_client):
    """Test not found error handling."""
    with patch.object(api_client, '_make_request', side_effect=NotFoundError("Product not found")):
        with pytest.raises(NotFoundError):
            await api_client.get_product_details("nonexistent")


@pytest.mark.asyncio
async def test_context_manager(api_client):
    """Test async context manager."""
    async with api_client as client:
        assert client._client is not None
    
    # Client should be closed after context
    assert api_client._client is None


@pytest.mark.asyncio
async def test_check_inventory_success(api_client):
    """Test successful inventory check."""
    mock_response = [
        {
            "warehouse_id": "W1",
            "warehouse_name": "Main Warehouse",
            "quantity": 100,
            "location": "City A"
        }
    ]
    
    with patch.object(api_client, '_make_request', return_value=mock_response):
        inventory = await api_client.check_inventory("123")
        
        assert len(inventory) == 1
        assert inventory[0].warehouse_id == "W1"
        assert inventory[0].quantity == 100
