{"openapi": "3.0.2", "info": {"title": "MCP Product Search", "version": "1.0.0", "description": "Managed Connectivity Point for Pharmaciaty Product Search"}, "paths": {"/mcp/products/": {"get": {"summary": "Search products", "description": "Proxy search to Pharmaciaty API", "parameters": [{"name": "search", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "warehouses", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "is_available", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "default": "is_active"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "default": "asc"}}], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Product search results", "content": {"application/json": {}}}, "401": {"description": "Unauthorized"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer"}}}}