from fastapi import APIRouter, Depends, Query, HTTPException
from app.config import PHARMACIATY_API_URL, PHARMACIATY_REFERER
from app.auth import get_bearer_token
from app.utils.http_client import async_get

router = APIRouter()

@router.get("/products/")
async def search_products(
    search: str = Query(...),
    warehouses: str = Query(...),
    is_available: bool = Query(True),
    page: int = Query(1),
    limit: int = Query(100),
    sort_by: str = Query("is_active"),
    sort_order: str = Query("asc"),
    token: str = Depends(get_bearer_token),
):
    params = {
        "sort_by": sort_by,
        "sort_order": sort_order,
        "page": page,
        "limit": limit,
        "search": search,
        "warehouses": warehouses,
        "is_available": str(is_available).lower()
    }
    headers = {
        "Authorization": f"Bearer {token}",
        "Referer": PHARMACIATY_REFERER,
        "User-Agent": "MCP-FastAPI/1.0"
    }
    response = await async_get(PHARMACIATY_API_URL, params=params, headers=headers)
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail=response.text)
    return response.json()