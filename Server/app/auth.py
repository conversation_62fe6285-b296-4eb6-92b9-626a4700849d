from fastapi import Head<PERSON>, HTTPException, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

bearer_scheme = HTTPBearer()

def get_bearer_token(credentials: HTTPAuthorizationCredentials = Security(bearer_scheme)):
    if not credentials or not credentials.scheme.lower() == "bearer" or not credentials.credentials:
        raise HTTPException(status_code=401, detail="Invalid or missing token")
    return credentials.credentials