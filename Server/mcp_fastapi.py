from fastapi import FastAPI, <PERSON><PERSON>, Query, HTTPException
import httpx

app = FastAPI()

PHARMACIATY_API_URL = "https://api.pharmaciaty.cloud/api/v1/products/"

@app.get("/mcp/products/")
async def mcp_search_products(
    search: str,
    warehouses: str,
    is_available: bool = True,
    page: int = 1,
    limit: int = 100,
    sort_by: str = "is_active",
    sort_order: str = "asc",
    authorization: str = Header(...),
):
    params = {
        "sort_by": sort_by,
        "sort_order": sort_order,
        "page": page,
        "limit": limit,
        "search": search,
        "warehouses": warehouses,
        "is_available": str(is_available).lower()
    }
    headers = {
        "Authorization": authorization,
        "User-Agent": "YourApp MCP/1.0",
        "Referer": "https://your-mcp-app/products/",
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(PHARMACIATY_API_URL, params=params, headers=headers)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        return response.json()