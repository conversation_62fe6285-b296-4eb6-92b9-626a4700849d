# MCP Project Documentation

## Overview

This project is a **Managed Connectivity Point (MCP)** built with FastAPI. It acts as a secure middleware between your front-end or automation tools and the Pharmaciaty product API. Key features include authentication, request validation, response transformation, and easy extensibility.

---

## Project Structure

```
mcp_project/
├── app/
│   ├── main.py
│   ├── config.py
│   ├── auth.py
│   ├── api/
│   │   └── products.py
│   └── utils/
│       └── http_client.py
├── requirements.txt
├── .env
└── mcp.json
```

---

## Installation

1. **Clone the repository**  
    ```bash
    git clone <your-repo-url>
    cd mcp_project
    ```

2. **Create virtual environment (optional but recommended)**
    ```bash
    python3 -m venv venv
    source venv/bin/activate
    ```

3. **Install dependencies**
    ```bash
    pip install -r requirements.txt
    ```

4. **Configure environment variables**  
    Edit `.env` to set:
    ```
    PHARMACIATY_API_URL=https://api.pharmaciaty.cloud/api/v1/products/
    PHARMACIATY_REFERER=https://portal.pharmaciaty.cloud/products/all
    ```

---

## Running Locally

```bash
uvicorn app.main:app --reload
```

Visit [http://localhost:8000/docs](http://localhost:8000/docs) to view interactive Swagger UI.

---

## API Documentation

### **Endpoint**

```
GET /mcp/products/
```

#### **Query Parameters**
| Name         | Type    | Required | Default    | Description                     |
|--------------|---------|----------|------------|---------------------------------|
| search       | string  | yes      |            | Product search term             |
| warehouses   | string  | yes      |            | Comma-separated warehouse codes |
| is_available | boolean | no       | true       | Filter available products       |
| page         | int     | no       | 1          | Page number                     |
| limit        | int     | no       | 100        | Results per page                |
| sort_by      | string  | no       | is_active  | Field to sort by                |
| sort_order   | string  | no       | asc        | asc/desc                        |

#### **Header**
- `Authorization: Bearer <your_jwt_token>`

---

## Authentication

Requests require a valid Bearer token (JWT). This is passed with the `Authorization` header.

---

## Testing the API

### **With cURL**

```bash
curl 'http://localhost:8000/mcp/products/?search=CISTAL-1&warehouses=V2FyZWhvdXNlOjk1NjA5,V2FyZWhvdXNlOjk3MTQ4&is_available=true' \
  -H 'Authorization: Bearer <your_jwt_token>'
```

### **With Swagger UI**

Open [http://localhost:8000/docs](http://localhost:8000/docs) in your browser, and use the interactive form to test endpoints.

---

## Testing with Claude or ChatGPT via Copilot (Locally)

You can use Copilot or any AI code assistant (including Claude or ChatGPT) to interact with your MCP in several ways:

### 1. **Prompting Copilot to Generate Requests**

- Open your code editor (e.g., VS Code) with Copilot enabled.
- Ask Copilot:  
  “Write a Python script to query the MCP `/mcp/products/` endpoint with a search term and Bearer token.”

Example prompt for Copilot/Claude/ChatGPT:
```
Write a Python script using requests to call http://localhost:8000/mcp/products/ with the search term "CISTAL-1", warehouses "V2FyZWhvdXNlOjk1NjA5,V2FyZWhvdXNlOjk3MTQ4", and a Bearer token "your_token_here".
```

### 2. **Sample Python Test Script**

```python name=test_mcp.py
import requests

url = "http://localhost:8000/mcp/products/"
params = {
    "search": "CISTAL-1",
    "warehouses": "V2FyZWhvdXNlOjk1NjA5,V2FyZWhvdXNlOjk3MTQ4",
    "is_available": "true"
}
headers = {
    "Authorization": "Bearer your_token_here"
}

response = requests.get(url, params=params, headers=headers)
print(response.json())
```

### 3. **Testing via AI Conversation**

- Ask Copilot/Claude/ChatGPT to review your code, suggest improvements, or automate API tests.
- You can also copy/paste your cURL or Python script into the chat and ask for troubleshooting or feature suggestions.

### 4. **Loading OpenAPI Spec**

- Claude/ChatGPT can review your `mcp.json` (OpenAPI file) for endpoint details.
- You can paste the spec or ask for test generation based on it.

---

## Extending the MCP

- Create new endpoints in `app/api/`.
- Add business logic, caching, advanced authentication, error handling, etc.

---

## Deployment

- For production, run with:
    ```bash
    uvicorn app.main:app --host 0.0.0.0 --port 80
    ```
- Use a process manager like Gunicorn, and deploy behind a reverse proxy (e.g., Nginx).

---

## Troubleshooting

- Check `.env` and Bearer token validity.
- Review FastAPI logs for errors.
- Ensure Pharmaciaty API credentials are correct.

---

## References

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [httpx](https://www.python-httpx.org/)
- [OpenAPI](https://swagger.io/specification/)

---

## License

MIT
