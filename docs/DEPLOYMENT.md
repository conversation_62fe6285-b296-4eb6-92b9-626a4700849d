# Deployment Guide

This guide covers various deployment options for the Pharmaciaty MCP Server.

## 📋 Table of Contents

- [Local Development](#local-development)
- [<PERSON>op Integration](#claude-desktop-integration)
- [VS Code Integration](#vs-code-integration)
- [ChatGPT Integration](#chatgpt-integration)
- [Docker Deployment](#docker-deployment)
- [Cloud Deployment](#cloud-deployment)
- [Production Considerations](#production-considerations)

## 🏠 Local Development

### Prerequisites

- Python 3.8+
- Pharmaciaty API token
- Git

### Setup

```bash
# Clone repository
git clone https://github.com/your-org/pharmaciaty-mcp
cd pharmaciaty-mcp

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your API token
```

### Running Locally

```bash
# MCP mode (for <PERSON>, VS Code)
python main.py --mode mcp

# HTTP mode (for ChatGPT, testing)
python main.py --mode chatgpt --host 0.0.0.0 --port 8000
```

## 🤖 Claude Desktop Integration

### Automatic Installation

```bash
# Install using MCP CLI (if available)
mcp install main.py --name "Pharmaciaty Products"
```

### Manual Installation

1. **Locate Claude Desktop config file:**
   - **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

2. **Add server configuration:**
   ```json
   {
     "mcpServers": {
       "pharmaciaty-products": {
         "command": "python",
         "args": ["/absolute/path/to/pharmaciaty-mcp/main.py", "--mode", "mcp"],
         "env": {
           "PHARMACIATY_API_TOKEN": "your_api_token_here",
           "PHARMACIATY_LOG_LEVEL": "INFO"
         }
       }
     }
   }
   ```

3. **Restart Claude Desktop**

### Using with Virtual Environment

```json
{
  "mcpServers": {
    "pharmaciaty-products": {
      "command": "/path/to/pharmaciaty-mcp/.venv/bin/python",
      "args": ["/path/to/pharmaciaty-mcp/main.py", "--mode", "mcp"],
      "env": {
        "PHARMACIATY_API_TOKEN": "your_api_token_here"
      }
    }
  }
}
```

## 🔧 VS Code Integration

### Using MCP Extension

1. **Install MCP extension** in VS Code
2. **Configure in VS Code settings** (`settings.json`):
   ```json
   {
     "mcp.servers": {
       "pharmaciaty-products": {
         "command": "python",
         "args": ["/path/to/pharmaciaty-mcp/main.py", "--mode", "mcp"],
         "env": {
           "PHARMACIATY_API_TOKEN": "your_api_token_here"
         }
       }
     }
   }
   ```

3. **Restart VS Code**

## 💬 ChatGPT Integration

### Local HTTP Server

```bash
# Start HTTP server
python main.py --mode chatgpt --host 0.0.0.0 --port 8000

# Test the server
curl http://localhost:8000/health
```

### Access Points

- **API Documentation**: `http://localhost:8000/docs`
- **Health Check**: `http://localhost:8000/health`
- **OpenAPI Spec**: `http://localhost:8000/openapi.json`

### Using with ChatGPT

1. **Deploy to a public URL** (see cloud deployment)
2. **Configure in ChatGPT** using the OpenAPI specification
3. **Use the tools** in your ChatGPT conversations

## 🐳 Docker Deployment

### Build and Run

```bash
# Build image
docker build -t pharmaciaty-mcp .

# Run container
docker run -d \
  --name pharmaciaty-mcp \
  -p 8000:8000 \
  -e PHARMACIATY_API_TOKEN=your_token_here \
  pharmaciaty-mcp
```

### Using Docker Compose

```bash
# Copy example compose file
cp examples/docker-compose.yml .

# Configure environment
echo "PHARMACIATY_API_TOKEN=your_token_here" > .env

# Start services
docker-compose up -d
```

### Health Check

```bash
# Check container health
docker ps
curl http://localhost:8000/health
```

## ☁️ Cloud Deployment

### Google Cloud Run

```bash
# Build and deploy
gcloud run deploy pharmaciaty-mcp \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars PHARMACIATY_API_TOKEN=your_token \
  --port 8000
```

### AWS ECS

1. **Push image to ECR:**
   ```bash
   # Build and tag
   docker build -t pharmaciaty-mcp .
   docker tag pharmaciaty-mcp:latest your-account.dkr.ecr.region.amazonaws.com/pharmaciaty-mcp:latest
   
   # Push to ECR
   docker push your-account.dkr.ecr.region.amazonaws.com/pharmaciaty-mcp:latest
   ```

2. **Create ECS task definition** with environment variables
3. **Deploy to ECS service**

### Azure Container Instances

```bash
# Deploy to Azure
az container create \
  --resource-group myResourceGroup \
  --name pharmaciaty-mcp \
  --image pharmaciaty-mcp:latest \
  --ports 8000 \
  --environment-variables PHARMACIATY_API_TOKEN=your_token
```

### Heroku

```bash
# Login and create app
heroku login
heroku create your-app-name

# Set environment variables
heroku config:set PHARMACIATY_API_TOKEN=your_token

# Deploy
git push heroku main
```

## 🔒 Production Considerations

### Security

- **Environment Variables**: Never commit API tokens to version control
- **HTTPS**: Always use HTTPS in production
- **Authentication**: Consider adding authentication for HTTP endpoints
- **Rate Limiting**: Implement rate limiting for public APIs

### Monitoring

- **Health Checks**: Use `/health` endpoint for monitoring
- **Logging**: Configure appropriate log levels
- **Metrics**: Monitor API response times and error rates

### Scaling

- **Horizontal Scaling**: Deploy multiple instances behind a load balancer
- **Resource Limits**: Set appropriate CPU and memory limits
- **Connection Pooling**: Configure HTTP client connection pooling

### Configuration

```bash
# Production environment variables
PHARMACIATY_API_TOKEN=your_production_token
PHARMACIATY_LOG_LEVEL=WARNING
PHARMACIATY_API_TIMEOUT=30
```

### Backup and Recovery

- **Configuration Backup**: Backup your configuration files
- **Environment Variables**: Document all required environment variables
- **Disaster Recovery**: Have a plan for service restoration

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :8000
   # Kill process or use different port
   python main.py --mode chatgpt --port 8001
   ```

2. **Permission Denied**
   ```bash
   # Fix file permissions
   chmod +x main.py
   ```

3. **Module Not Found**
   ```bash
   # Ensure Python path is correct
   export PYTHONPATH=/path/to/pharmaciaty-mcp/src:$PYTHONPATH
   ```

### Debug Mode

```bash
# Enable debug logging
python main.py --mode chatgpt --log-level DEBUG
```

### Health Check

```bash
# Test API connectivity
curl -v http://localhost:8000/health
```
