# Core MCP dependencies
fastmcp>=2.10.0

# HTTP client and async support
httpx>=0.27.0
aiofiles>=23.0.0

# Configuration and environment
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Logging and monitoring
structlog>=23.0.0

# ChatGPT integration
openai>=1.0.0

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-httpx>=0.21.0
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0

# Deployment
uvicorn>=0.27.0