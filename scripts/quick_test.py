#!/usr/bin/env python3
"""
Quick test script for Pharmaciaty MCP Server.

This script performs a quick test of all major functionality to ensure
the server is working correctly.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pharmaciaty_mcp.api.client import PharmaciatyAPIClient
from pharmaciaty_mcp.utils.logging import setup_logging, get_logger


async def test_api_connectivity():
    """Test basic API connectivity."""
    print("🔗 Testing API connectivity...")
    
    try:
        async with PharmaciatyAPIClient() as client:
            # Test a simple search
            result = await client.search_products(search="test", limit=1)
            print(f"✅ API connectivity successful - found {result.total_count} products")
            return True
    except Exception as e:
        print(f"❌ API connectivity failed: {e}")
        return False


async def test_product_search():
    """Test product search functionality."""
    print("\n🔍 Testing product search...")
    
    try:
        async with PharmaciatyAPIClient() as client:
            result = await client.search_products(search="paracetamol", limit=3)
            print(f"✅ Search successful - found {len(result.products)} products")
            
            if result.products:
                product = result.products[0]
                print(f"   Sample product: {product.name}")
                print(f"   Price: {product.price} {product.currency}")
            
            return True
    except Exception as e:
        print(f"❌ Product search failed: {e}")
        return False


async def test_product_details():
    """Test product details functionality."""
    print("\n📋 Testing product details...")
    
    try:
        async with PharmaciatyAPIClient() as client:
            # Get a product from search first
            search_result = await client.search_products(search="vitamin", limit=1)
            
            if search_result.products:
                product_id = search_result.products[0].id
                product = await client.get_product_details(product_id)
                print(f"✅ Product details successful")
                print(f"   Product: {product.name}")
                print(f"   Description: {product.description[:100] if product.description else 'N/A'}...")
                return True
            else:
                print("⚠️  No products found for details test")
                return True
                
    except Exception as e:
        print(f"❌ Product details failed: {e}")
        return False


async def test_category_search():
    """Test category search functionality."""
    print("\n🏷️ Testing category search...")
    
    try:
        async with PharmaciatyAPIClient() as client:
            result = await client.search_products(category="Pain Management", limit=2)
            print(f"✅ Category search successful - found {len(result.products)} products")
            
            if result.products:
                for product in result.products:
                    print(f"   - {product.name}")
            
            return True
    except Exception as e:
        print(f"❌ Category search failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Pharmaciaty MCP Server - Quick Test")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    tests = [
        test_api_connectivity,
        test_product_search,
        test_product_details,
        test_category_search
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your MCP server is ready to use.")
        print("\nNext steps:")
        print("1. Start MCP server: python main.py --mode mcp")
        print("2. Start ChatGPT server: python main.py --mode chatgpt --port 8000")
        print("3. Register with Claude Desktop using examples/claude_desktop_config.json")
    else:
        print("⚠️  Some tests failed. Check your configuration:")
        print("1. Verify PHARMACIATY_API_TOKEN in .env")
        print("2. Check internet connectivity")
        print("3. Ensure API access permissions")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
