import os
import httpx
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi_mcp import MCP
from starlette.status import HTTP_502_BAD_GATEWAY

PHARMACIATY_API_BASE = "https://api.pharmaciaty.cloud/api/v1"

app = FastAPI(title="Pharmaciaty Products MCP", version="1.0.0")
mcp = MCP(app)

@app.get("/health", tags=["Health"])
async def health():
    """Health check endpoint."""
    return {"status": "ok"}

@app.get("/products", tags=["Products"])
async def proxy_products(request: Request):
    """
    Proxies GET /products with all query parameters and forwards Authorization header if present.
    """
    # Collect query parameters
    params = dict(request.query_params)
    # Prepare headers
    headers = {}
    auth = request.headers.get("authorization")
    if auth:
        headers["authorization"] = auth
    # Forward the request
    url = f"{PHARMACIATY_API_BASE}/products/"
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, params=params, headers=headers)
        return JSONResponse(status_code=resp.status_code, content=resp.json())
    except Exception as e:
        raise HTTPException(status_code=HTTP_502_BAD_GATEWAY, detail=f"Upstream error: {str(e)}")

mcp.mount()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)