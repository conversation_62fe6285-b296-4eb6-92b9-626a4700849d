import os
import httpx
from typing import Optional, List, Dict, Any, Annotated
from dataclasses import dataclass
from dotenv import load_dotenv
from fastmcp import FastMCP
from fastmcp.integrations.chatgpt import ChatGPTRouter

# Load environment variables
load_dotenv(override=True)

# Configuration
PHARMACIATY_API_BASE = "https://api.pharmaciaty.cloud/api/v1"
DEFAULT_TOKEN = ""

# Create MCP server
mcp = FastMCP("Pharmaciaty Products API")

# Add ChatGPT integration
chatgpt_router = ChatGPTRouter(
    mcp, 
    description="Pharmaciaty Products API for searching products, checking inventory, and product details",
    openai_api_key=os.getenv("OPENAI_API_KEY", "")
)

@dataclass
class ProductSearchResult:
    """Structured result for product search"""
    total_count: int
    page: int
    limit: int
    products: List[Dict[str, Any]]
    search_query: Optional[str] = None
    warehouses: Optional[str] = None

async def make_api_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    token: Optional[str] = None
) -> Dict[str, Any]:
    """Make authenticated request to Pharmaciaty API"""
    headers = {
        "Authorization": f"Bearer {token or os.getenv('PHARMACIATY_TOKEN', DEFAULT_TOKEN)}",
        "User-Agent": "Pharmaciaty-MCP-Server/1.0.0"
    }

    url = f"{PHARMACIATY_API_BASE}{endpoint}"

    print(f"Making API request to {url} with params: {params} and headers: {headers}")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, params=params or {}, headers=headers)
            response.raise_for_status()
            print(f"API response status: {response.status_code}")
            return response.json()
    except httpx.HTTPStatusError as e:
        raise Exception(f"API request failed with status {e.response.status_code}: {e.response.text}")
    except httpx.RequestError as e:
        raise Exception(f"Request error: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error: {str(e)}")

@mcp.tool
async def search_products(
    search: Annotated[str, "Search term to find products (e.g., product name, brand, ingredient)"] = "",
    limit: Annotated[int, "Number of products per page (max: 100)"] = 20,
    is_available: Annotated[bool, "Filter for available products only"] = True
) -> Dict[str, Any]:
    """
    Search for products in the Pharmaciaty catalog.

    Search the product catalog by name, brand, or ingredients. Results include
    product details such as price, composition, and availability.

    Args:
        search: Search term to find products (e.g., product name, brand)
        limit: Number of products per page (default: 20, max: 100)
        is_available: Filter for available products only (default: True)

    Returns:
        A dictionary containing the response with "message" and "data" keys.
    """
    params = {
        "page": 1,
        "limit": min(limit, 100),  # Cap at 100
        "is_available": is_available
    }

    if search:
        params["search"] = search
    
    try:
        result = await make_api_request("/products/", params)
        if not result.get("data",[]):
            return {
                "message": "No products found",
                "data": []
            }
        print(f"Found {result.get('count', 0)} products matching '{search}'")
        
        return {
            "message": None,
            "data": result.get("data", [])
        }
    except Exception as e:
        raise Exception(f"Failed to search products: {str(e)}")

@mcp.tool
async def get_product_details(
    product_id: Annotated[str, "The unique identifier of the product"]
) -> Dict[str, Any]:
    """
    Get detailed information about a specific product.
    
    Fetches comprehensive product information including description, composition,
    price, categories, and availability.

    Args:
        product_id: The unique identifier of the product

    Returns:
        Detailed product information including composition, pricing, and categories
    """
    try:
        result = await make_api_request(f"/products/{product_id}")
        return result
    except Exception as e:
        raise Exception(f"Failed to get product details: {str(e)}")

@mcp.tool
async def search_products_by_category(
    category: Annotated[str, "Product category to search for (e.g. 'Pain Management', 'Children Health')"],
    page: Annotated[int, "Page number for pagination"] = 1,
    limit: Annotated[int, "Number of products per page (max: 100)"] = 20,
    is_available: Annotated[bool, "Filter for available products only"] = True
) -> ProductSearchResult:
    """
    Search for products by category.
    
    Find all products within a specified category such as 'Pain Management',
    'Children Health', 'Skin Care', etc. Results can be paginated and filtered
    by availability.

    Args:
        category: Product category to search for
        page: Page number for pagination (default: 1)
        limit: Number of products per page (default: 20, max: 100)
        is_available: Filter for available products only (default: True)

    Returns:
        ProductSearchResult with products in the specified category
    """
    params = {
        "category": category,
        "page": page,
        "limit": min(limit, 100),
        "is_available": is_available
    }

    try:
        result = await make_api_request("/products/", params)

        return ProductSearchResult(
            total_count=result.get("count", 0),
            page=page,
            limit=limit,
            products=result.get("results", []),
            search_query=f"category:{category}"
        )
    except Exception as e:
        raise Exception(f"Failed to search products by category: {str(e)}")

@mcp.tool
async def check_inventory(
    product_id: Annotated[str, "The unique identifier of the product"]
) -> Dict[str, Any]:
    """
    Check the inventory for a specific product across all warehouses.

    Get current stock levels and availability information for a product
    in various warehouses and cities. Useful for verifying if a product
    is available in specific locations.

    Args:
        product_id: The unique identifier of the product

    Returns:
        Inventory details for the product including available quantity and locations
    """
    try:
        result = await make_api_request(f"/products/{product_id}/stock")
        return result
    except Exception as e:
        raise Exception(f"Failed to check inventory for product {product_id}: {str(e)}")

if __name__ == "__main__":
    print("Starting Pharmaciaty Catalog MCP server...")
    print("Server will be available at http://0.0.0.0:8000")
    print("ChatGPT integration enabled")
    
    # Run the server with the ChatGPT integration
    mcp.run(host="0.0.0.0", port=8000)