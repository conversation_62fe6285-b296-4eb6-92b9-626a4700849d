#!/usr/bin/env python3
"""
Pharmaciaty MCP Server - Main Entry Point

A production-grade Model Context Protocol server for the Pharmaciaty Products API.

Usage:
    python main.py [--mode mcp|chatgpt] [--host HOST] [--port PORT]

Modes:
    mcp     - Run as standard MCP server (default, for Claude Desktop, VS Code)
    chatgpt - Run as HTTP server with ChatGPT integration
"""

import argparse
import sys
from pathlib import Path

# Add src to Python path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pharmaciaty_mcp.server.mcp_server import run_server
from pharmaciaty_mcp.integrations.chatgpt import run_chatgpt_server
from pharmaciaty_mcp.utils.logging import setup_logging, get_logger


def main():
    """Main entry point for the Pharmaciaty MCP Server."""
    parser = argparse.ArgumentParser(
        description="Pharmaciaty MCP Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        "--mode",
        choices=["mcp", "chatgpt"],
        default="mcp",
        help="Server mode: 'mcp' for standard MCP (Claude/VS Code), 'chatgpt' for HTTP (ChatGPT)"
    )

    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Host to bind the server to (only for ChatGPT mode)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (only for ChatGPT mode)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging()
    logger = get_logger("Main")

    logger.info(
        "Starting Pharmaciaty MCP Server",
        mode=args.mode,
        host=args.host if args.mode == "chatgpt" else "stdio",
        port=args.port if args.mode == "chatgpt" else None
    )

    try:
        if args.mode == "mcp":
            # Run standard MCP server (for Claude Desktop, VS Code)
            logger.info("Running in MCP mode (Claude Desktop, VS Code)")
            run_server()
        elif args.mode == "chatgpt":
            # Run HTTP server with ChatGPT integration
            logger.info("Running in ChatGPT mode (HTTP server)")
            run_chatgpt_server(host=args.host, port=args.port)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Server failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()