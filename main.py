import os
import httpx
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
PHARMACIATY_API_BASE = "https://api.pharmaciaty.cloud/api/v1"
DEFAULT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.ezWdQ7lnhZm6WntsyoVqMGJvL_pw-2KWHBOKcpPtWk4"

# Create MCP server
mcp = FastMCP("Pharmaciaty Products API")

@dataclass
class ProductSearchResult:
    """Structured result for product search"""
    total_count: int
    page: int
    limit: int
    products: List[Dict[str, Any]]
    search_query: Optional[str] = None
    warehouses: Optional[str] = None

async def make_api_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    token: Optional[str] = None
) -> Dict[str, Any]:
    """Make authenticated request to Pharmaciaty API"""
    headers = {
        "Authorization": f"bearer {token or os.getenv('PHARMACIATY_TOKEN', DEFAULT_TOKEN)}",
        "User-Agent": "Pharmaciaty-MCP-Server/1.0.0"
    }

    url = f"{PHARMACIATY_API_BASE}{endpoint}"

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, params=params or {}, headers=headers)
            response.raise_for_status()
            return response.json()
    except httpx.HTTPStatusError as e:
        raise Exception(f"API request failed with status {e.response.status_code}: {e.response.text}")
    except httpx.RequestError as e:
        raise Exception(f"Request error: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error: {str(e)}")

@mcp.tool()
async def search_products(
    search: Optional[str] = None,
    page: int = 1,
    limit: int = 20,
    sort_by: str = "is_active",
    sort_order: str = "asc",
    warehouses: Optional[str] = None,
    is_available: bool = True
) -> ProductSearchResult:
    """
    Search for products in the Pharmaciaty catalog.

    Args:
        search: Search term to find products (e.g., product name, brand)
        page: Page number for pagination (default: 1)
        limit: Number of products per page (default: 20, max: 100)
        sort_by: Field to sort by (default: "is_active")
        sort_order: Sort order "asc" or "desc" (default: "asc")
        warehouses: Comma-separated warehouse IDs (encoded)
        is_available: Filter for available products only (default: True)

    Returns:
        ProductSearchResult with products and metadata
    """
    params = {
        "page": page,
        "limit": min(limit, 100),  # Cap at 100
        "sort_by": sort_by,
        "sort_order": sort_order,
        "is_available": is_available
    }

    if search:
        params["search"] = search
    if warehouses:
        params["warehouses"] = warehouses

    try:
        result = await make_api_request("/products/", params)

        return ProductSearchResult(
            total_count=result.get("count", 0),
            page=page,
            limit=limit,
            products=result.get("results", []),
            search_query=search,
            warehouses=warehouses
        )
    except Exception as e:
        raise Exception(f"Failed to search products: {str(e)}")

@mcp.tool()
async def get_product_details(product_id: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific product.

    Args:
        product_id: The unique identifier of the product

    Returns:
        Detailed product information
    """
    try:
        result = await make_api_request(f"/products/{product_id}/")
        return result
    except Exception as e:
        raise Exception(f"Failed to get product details: {str(e)}")

@mcp.tool()
async def search_products_by_category(
    category: str,
    page: int = 1,
    limit: int = 20,
    is_available: bool = True
) -> ProductSearchResult:
    """
    Search for products by category.

    Args:
        category: Product category to search for
        page: Page number for pagination (default: 1)
        limit: Number of products per page (default: 20, max: 100)
        is_available: Filter for available products only (default: True)

    Returns:
        ProductSearchResult with products in the specified category
    """
    params = {
        "category": category,
        "page": page,
        "limit": min(limit, 100),
        "is_available": is_available
    }

    try:
        result = await make_api_request("/products/", params)

        return ProductSearchResult(
            total_count=result.get("count", 0),
            page=page,
            limit=limit,
            products=result.get("results", []),
            search_query=f"category:{category}"
        )
    except Exception as e:
        raise Exception(f"Failed to search products by category: {str(e)}")

if __name__ == "__main__":
    mcp.run()