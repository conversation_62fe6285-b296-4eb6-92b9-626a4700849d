# Pharmaciaty MCP Server Configuration
# Copy this file to .env and update with your actual values

# Required: Pharmaciaty API Configuration
PHARMACIATY_API_TOKEN=your_actual_token_here

# Optional: API Configuration
PHARMACIATY_API_BASE_URL=https://api.pharmaciaty.cloud/api/v1
PHARMACIATY_API_TIMEOUT=30

# Optional: Server Configuration
PHARMACIATY_SERVER_NAME=Pharmaciaty Products API
PHARMACIATY_SERVER_VERSION=1.0.0

# Optional: Logging Configuration
PHARMACIATY_LOG_LEVEL=INFO
PHARMACIATY_LOG_FORMAT=json

# Optional: ChatGPT Integration
# PHARMACIATY_OPENAI_API_KEY=your_openai_api_key_here

# Optional: Performance Configuration
PHARMACIATY_RATE_LIMIT_REQUESTS=100
PHARMACIATY_CACHE_TTL=300
