# Copilot Instructions for NexaOps Catalog MCP

## Overview
This codebase is for the NexaOps Catalog MCP server, built using FastAPI and MCP (Managed Connectivity Point) framework. It integrates with the Pharmaciaty API to provide tools for searching products, fetching product details, and checking inventory.

## Architecture
- **Main Components**:
  - `main.py`: Core file containing MCP tools and API interaction logic.
  - `claude_desktop_config.json`: Configuration file for running the MCP server.
- **Service Boundaries**:
  - MCP server communicates with the Pharmaciaty API using HTTP requests.
  - Tools are defined as MCP methods and exposed via CLI or other transports.
- **Data Flow**:
  - Tools in `main.py` make authenticated API requests to Pharmaciaty endpoints.
  - Responses are processed and returned in structured formats.

## Developer Workflows
### Running the MCP Server
1. Ensure the Python environment is set up:
   ```bash
   source /Users/<USER>/work/NexaOps/CatelogMCP/.venv/bin/activate
   ```
2. Start the server using the command defined in `claude_desktop_config.json`:
   ```bash
   /Users/<USER>/work/NexaOps/CatelogMCP/.venv/bin/uv run --with mcp[cli] mcp run /Users/<USER>/work/NexaOps/CatelogMCP/main.py
   ```

### Testing Tools
- Use the MCP CLI to test individual tools:
  ```bash
  mcp tool invoke search_products --search="pain management" --limit=10
  ```

## Project-Specific Conventions
- **Tool Definitions**:
  - Tools are decorated with `@mcp.tool()` in `main.py`.
  - Each tool has clear docstrings describing its purpose and arguments.
- **API Requests**:
  - Use `make_api_request` for all HTTP interactions.
  - Handle errors explicitly using `try-except` blocks.
- **Environment Variables**:
  - Load API tokens and other configurations using `dotenv`.

## Integration Points
- **External Dependencies**:
  - Pharmaciaty API: Base URL and endpoints are configured in `main.py`.
  - `dotenv`: Used for managing environment variables.
- **Cross-Component Communication**:
  - Tools interact with the Pharmaciaty API and return structured results.

## Examples
### Adding a New Tool
1. Define the tool in `main.py`:
   ```python
   @mcp.tool()
   async def new_tool(arg1: str) -> Dict[str, Any]:
       """Description of the tool."""
       result = await make_api_request("/new-endpoint", {"param": arg1})
       return result
   ```
2. Test the tool using MCP CLI.

### Debugging API Requests
- Print debug information in `make_api_request`:
  ```python
  print(f"Making API request to {url} with params: {params}")
  ```

## Key Files
- `main.py`: Core logic for MCP tools.
- `claude_desktop_config.json`: Server configuration.
- `requirements.txt`: Python dependencies.

Let me know if any sections need clarification or additional details!
