"""
Pharmaciaty MCP Server

A production-grade Model Context Protocol server for the Pharmaciaty Products API.
Provides AI assistants with tools to search products, check inventory, and retrieve
detailed product information.
"""

__version__ = "1.0.0"
__author__ = "Pharmaciaty Team"
__email__ = "<EMAIL>"

from .api.client import PharmaciatyAPIClient
from .server.mcp_server import create_mcp_server
from .integrations.chatgpt import create_chatgpt_server

__all__ = [
    "PharmaciatyAPIClient",
    "create_mcp_server", 
    "create_chatgpt_server",
]
