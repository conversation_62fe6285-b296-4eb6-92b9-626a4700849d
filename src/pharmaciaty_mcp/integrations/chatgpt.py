"""
ChatGPT integration for Pharmaciaty MCP Server.

This module provides HTTP-based access to the MCP server for ChatGPT Deep Research.
ChatGPT requires exactly two tools: 'search' and 'fetch' for Deep Research functionality.
"""

from typing import Optional, List, Dict, Any
from fastmcp import FastMCP

from ..config import config
from ..api.client import PharmaciatyAPIClient
from ..utils.logging import get_logger, setup_logging


def create_chatgpt_server() -> FastMCP:
    """
    Create a FastMCP server compatible with ChatGPT Deep Research.

    ChatGPT requires exactly two tools for Deep Research:
    - search: Takes a query and returns matching product IDs
    - fetch: Takes an ID and returns the complete product information

    Returns:
        Configured FastMCP server for ChatGPT Deep Research
    """
    logger = get_logger("ChatGPTIntegration")

    # Setup logging
    setup_logging()

    # Create FastMCP server
    mcp = FastMCP(
        name="Pharmaciaty Products Deep Research",
        instructions=(
            "A comprehensive pharmaceutical products database for deep research. "
            "Search for medications, check availability, and get detailed product information "
            "including composition, pricing, and inventory across multiple warehouses."
        )
    )

    # Initialize API client
    api_client = PharmaciatyAPIClient()

    @mcp.tool()
    async def search(query: str) -> Dict[str, List[str]]:
        """
        Search for pharmaceutical products by name, brand, ingredient, or category.

        This tool searches the Pharmaciaty product catalog and returns matching product IDs.
        You can search for:
        - Product names (e.g., "Paracetamol", "Aspirin")
        - Brand names (e.g., "Pfizer", "GSK")
        - Active ingredients (e.g., "acetaminophen", "ibuprofen")
        - Categories (e.g., "Pain Management", "Antibiotics")
        - Medical conditions (e.g., "diabetes", "hypertension")

        The search is comprehensive and will find products even with partial matches.
        Use this tool first to find relevant products, then use fetch to get detailed information.

        Args:
            query: Search term for finding products

        Returns:
            Dictionary with 'ids' key containing list of matching product IDs
        """
        try:
            logger.info("ChatGPT search request", query=query)

            # Search for products
            result = await api_client.search_products(
                search=query,
                limit=50,  # Get more results for better coverage
                is_available=True
            )

            # Extract product IDs
            product_ids = [product.id for product in result.products]

            logger.info(
                "ChatGPT search completed",
                query=query,
                found_count=len(product_ids),
                total_available=result.total_count
            )

            return {"ids": product_ids}

        except Exception as e:
            logger.error("ChatGPT search failed", query=query, error=str(e))
            return {"ids": []}
    @mcp.tool()
    async def fetch(id: str) -> Dict[str, Any]:
        """
        Fetch detailed information for a specific pharmaceutical product by ID.

        This tool retrieves comprehensive product information including:
        - Product name, brand, and description
        - Active ingredients and composition
        - Pricing and currency information
        - Availability status and inventory
        - Category and therapeutic classification
        - Regulatory and safety information

        Use this tool after using search to get complete details about specific products.
        The information returned can be used for detailed analysis, comparison, and citation.

        Args:
            id: The unique product identifier from search results

        Returns:
            Complete product information dictionary
        """
        try:
            logger.info("ChatGPT fetch request", product_id=id)

            # Get detailed product information
            product = await api_client.get_product_details(id)

            # Convert to dictionary for ChatGPT
            product_data = {
                "id": product.id,
                "name": product.name,
                "brand": product.brand,
                "description": product.description,
                "composition": product.composition,
                "category": product.category,
                "price": product.price,
                "currency": product.currency,
                "is_available": product.is_available,
                "is_active": product.is_active,
                "image_url": product.image_url
            }

            # Try to get inventory information
            try:
                inventory = await api_client.check_inventory(id)
                if inventory:
                    product_data["inventory"] = [
                        {
                            "warehouse_id": item.warehouse_id,
                            "warehouse_name": item.warehouse_name,
                            "quantity": item.quantity,
                            "location": item.location
                        }
                        for item in inventory
                    ]
                    product_data["total_quantity"] = sum(item.quantity for item in inventory)
            except Exception as inv_error:
                logger.warning("Could not fetch inventory", product_id=id, error=str(inv_error))
                product_data["inventory_note"] = "Inventory information not available"

            logger.info("ChatGPT fetch completed", product_id=id, product_name=product.name)

            return product_data

        except Exception as e:
            logger.error("ChatGPT fetch failed", product_id=id, error=str(e))
            raise ValueError(f"Product with ID '{id}' not found or unavailable")

    logger.info("ChatGPT Deep Research server created")

    return mcp


def run_chatgpt_server(
    host: str = "0.0.0.0",
    port: int = 8000
):
    """
    Run the ChatGPT Deep Research MCP server.

    Args:
        host: Host to bind the server to
        port: Port to bind the server to
    """
    logger = get_logger("ChatGPTServer")

    try:
        mcp = create_chatgpt_server()

        logger.info(
            "Starting ChatGPT Deep Research MCP server",
            host=host,
            port=port,
            server_name="Pharmaciaty Products Deep Research"
        )

        # Run with HTTP transport for ChatGPT
        mcp.run(transport="http", host=host, port=port)

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Failed to start ChatGPT server", error=str(e))
        raise


if __name__ == "__main__":
    run_chatgpt_server()
