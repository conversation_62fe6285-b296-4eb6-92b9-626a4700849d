"""
ChatGPT integration for Pharmaciaty MCP Server.

This module provides HTTP-based access to the MCP server for ChatGPT and other
HTTP-based AI clients.
"""

from typing import Optional
from fastmcp import FastMCP
from fastmcp.integrations.chatgpt import Chat<PERSON>TRouter

from ..config import config
from ..server.mcp_server import create_mcp_server
from ..utils.logging import get_logger


def create_chatgpt_server(
    openai_api_key: Optional[str] = None,
    host: str = "0.0.0.0",
    port: int = 8000
) -> FastMCP:
    """
    Create a FastMCP server with ChatGPT integration.
    
    This creates an HTTP server that can be accessed by ChatGPT and other
    HTTP-based AI clients. The server exposes all MCP tools as HTTP endpoints
    with proper OpenAPI documentation.
    
    Args:
        openai_api_key: OpenAI API key for ChatGPT integration
        host: Host to bind the server to
        port: Port to bind the server to
        
    Returns:
        Configured FastMCP server with ChatGPT integration
    """
    logger = get_logger("ChatGPTIntegration")
    
    # Create the base MCP server
    mcp = create_mcp_server()
    
    # Add ChatGPT integration
    api_key = openai_api_key or config.openai_api_key
    if not api_key:
        logger.warning("No OpenAI API key provided - ChatGPT integration will be limited")
    
    chatgpt_router = ChatGPTRouter(
        mcp,
        description=(
            "Pharmaciaty Products API for searching pharmaceutical products, "
            "checking inventory, and retrieving detailed product information. "
            "This API provides access to a comprehensive catalog of pharmaceutical "
            "products with real-time availability and pricing information."
        ),
        openai_api_key=api_key
    )
    
    # Add ChatGPT-specific endpoints
    @mcp.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "name": config.server_name,
            "version": config.server_version,
            "description": "Pharmaciaty Products API MCP Server with ChatGPT Integration",
            "endpoints": {
                "health": "/health",
                "docs": "/docs",
                "openapi": "/openapi.json",
                "mcp": "/mcp/"
            },
            "integrations": ["ChatGPT", "OpenAI", "HTTP Clients"],
            "documentation": "https://github.com/your-org/pharmaciaty-mcp"
        }
    
    @mcp.get("/health")
    async def health():
        """Health check endpoint for monitoring."""
        try:
            # Test API connectivity
            context = mcp.get_context()
            if context and context.lifespan_context:
                api_client = context.lifespan_context["api_client"]
                await api_client._ensure_client()
                api_status = "connected"
            else:
                api_status = "not_initialized"
            
            return {
                "status": "healthy",
                "api_status": api_status,
                "server": config.server_name,
                "version": config.server_version
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "server": config.server_name,
                "version": config.server_version
            }
    
    logger.info(
        "ChatGPT integration configured",
        host=host,
        port=port,
        has_openai_key=bool(api_key)
    )
    
    return mcp


def run_chatgpt_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    openai_api_key: Optional[str] = None
):
    """
    Run the ChatGPT-integrated MCP server.
    
    Args:
        host: Host to bind the server to
        port: Port to bind the server to
        openai_api_key: OpenAI API key for ChatGPT integration
    """
    logger = get_logger("ChatGPTServer")
    
    try:
        mcp = create_chatgpt_server(openai_api_key, host, port)
        
        logger.info(
            "Starting ChatGPT-integrated MCP server",
            host=host,
            port=port,
            server_name=config.server_name
        )
        
        # Run with HTTP transport
        mcp.run(transport="http", host=host, port=port)
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Failed to start ChatGPT server", error=str(e))
        raise


if __name__ == "__main__":
    run_chatgpt_server()
