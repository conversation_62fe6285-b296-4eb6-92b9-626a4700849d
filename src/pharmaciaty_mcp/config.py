"""
Configuration management for Pharmaciaty MCP Server.
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class PharmaciatyConfig(BaseSettings):
    """Configuration for Pharmaciaty MCP Server."""
    
    # API Configuration
    api_base_url: str = Field(
        default="https://api.pharmaciaty.cloud/api/v1",
        description="Base URL for Pharmaciaty API"
    )
    api_token: str = Field(
        description="Bearer token for Pharmaciaty API authentication"
    )
    api_timeout: int = Field(
        default=30,
        description="API request timeout in seconds"
    )
    
    # Server Configuration
    server_name: str = Field(
        default="Pharmaciaty Products API",
        description="Name of the MCP server"
    )
    server_version: str = Field(
        default="1.0.0",
        description="Version of the MCP server"
    )
    
    # Logging Configuration
    log_level: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR)"
    )
    log_format: str = Field(
        default="json",
        description="Log format (json, console)"
    )
    
    # ChatGPT Integration
    openai_api_key: Optional[str] = Field(
        default=None,
        description="OpenAI API key for ChatGPT integration"
    )
    
    # Rate Limiting
    rate_limit_requests: int = Field(
        default=100,
        description="Maximum requests per minute"
    )
    
    # Cache Configuration
    cache_ttl: int = Field(
        default=300,
        description="Cache TTL in seconds"
    )
    
    class Config:
        env_file = ".env"
        env_prefix = "PHARMACIATY_"
        case_sensitive = False


def get_config() -> PharmaciatyConfig:
    """Get the current configuration."""
    return PharmaciatyConfig()


# Global config instance
config = get_config()
