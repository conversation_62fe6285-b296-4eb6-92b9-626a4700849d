"""
Main MCP server implementation for Pharmaciaty Products API.
"""

from contextlib import asynccontextmanager
from typing import As<PERSON><PERSON><PERSON><PERSON>
from fastmcp import FastMCP

from ..config import config
from ..api.client import PharmaciatyAPIClient
from ..utils.logging import setup_logging, get_logger
from .tools import ProductTools


@asynccontextmanager
async def lifespan_manager() -> AsyncIterator[dict]:
    """
    Manage the lifecycle of the MCP server.
    
    Sets up logging, initializes the API client, and ensures proper cleanup.
    """
    # Setup logging
    setup_logging()
    logger = get_logger("MCPServer")
    
    logger.info("Starting Pharmaciaty MCP Server", version=config.server_version)
    
    # Initialize API client
    api_client = PharmaciatyAPIClient()
    
    try:
        # Test API connection
        await api_client._ensure_client()
        logger.info("API client initialized successfully")
        
        yield {"api_client": api_client}
        
    except Exception as e:
        logger.error("Failed to initialize API client", error=str(e))
        raise
    finally:
        # Cleanup
        await api_client.close()
        logger.info("MCP Server shutdown complete")


def create_mcp_server() -> FastMCP:
    """
    Create and configure the FastMCP server.
    
    Returns:
        Configured FastMCP server instance
    """
    # Create FastMCP server with lifespan management
    mcp = FastMCP(
        name=config.server_name,
        version=config.server_version,
        lifespan=lifespan_manager
    )
    
    # Add server info as a resource
    @mcp.resource("server://info")
    async def server_info() -> dict:
        """Provides information about the MCP server."""
        return {
            "name": config.server_name,
            "version": config.server_version,
            "description": "Pharmaciaty Products API MCP Server",
            "api_base_url": config.api_base_url,
            "capabilities": [
                "product_search",
                "product_details", 
                "inventory_check",
                "category_search"
            ],
            "supported_integrations": [
                "Claude Desktop",
                "VS Code",
                "ChatGPT",
                "Custom MCP Clients"
            ]
        }
    
    @mcp.resource("server://health")
    async def health_check() -> dict:
        """Health check endpoint for the server."""
        try:
            # Get API client from lifespan context
            context = mcp.get_context()
            api_client = context.lifespan_context["api_client"]
            
            # Test API connectivity
            await api_client._ensure_client()
            
            return {
                "status": "healthy",
                "api_connection": "ok",
                "timestamp": "2025-01-11T11:00:00Z"  # In production, use actual timestamp
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "api_connection": "failed",
                "error": str(e),
                "timestamp": "2025-01-11T11:00:00Z"
            }
    
    # Register tools after server creation
    def setup_tools():
        """Setup tools with API client from lifespan context."""
        context = mcp.get_context()
        if context and context.lifespan_context:
            api_client = context.lifespan_context["api_client"]
            ProductTools(mcp, api_client)
    
    # Register the setup function to be called after lifespan starts
    mcp.on_startup(setup_tools)
    
    return mcp


def run_server():
    """
    Run the MCP server.
    
    This function can be called directly or used as an entry point.
    """
    setup_logging()
    logger = get_logger("MCPServer")
    
    try:
        mcp = create_mcp_server()
        logger.info("Starting MCP server", name=config.server_name)
        mcp.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Server failed to start", error=str(e))
        raise


if __name__ == "__main__":
    run_server()
