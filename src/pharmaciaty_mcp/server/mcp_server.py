"""
Main MCP server implementation for Pharmaciaty Products API.
"""

from typing import Optional
from fastmcp import FastMCP

from ..config import config
from ..api.client import PharmaciatyAPIClient
from ..utils.logging import setup_logging, get_logger
from .tools import ProductTools


def create_mcp_server() -> FastMCP:
    """
    Create and configure the FastMCP server.

    Returns:
        Configured FastMCP server instance
    """
    # Setup logging
    setup_logging()
    logger = get_logger("MCPServer")

    # Create FastMCP server
    mcp = FastMCP(
        name=config.server_name,
        instructions=(
            "Pharmaciaty Products API MCP Server provides comprehensive access to "
            "pharmaceutical product information including search, inventory, and detailed "
            "product data. Use the tools to search for medications, check availability, "
            "and retrieve complete product specifications."
        )
    )

    # Initialize API client
    api_client = PharmaciatyAPIClient()

    # Register tools
    ProductTools(mcp, api_client)

    logger.info("MCP Server created", name=config.server_name, version=config.server_version)

    return mcp


def run_server():
    """
    Run the MCP server.

    This function can be called directly or used as an entry point.
    """
    logger = get_logger("MCPServer")

    try:
        mcp = create_mcp_server()
        logger.info("Starting MCP server", name=config.server_name)
        mcp.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Server failed to start", error=str(e))
        raise


if __name__ == "__main__":
    run_server()
