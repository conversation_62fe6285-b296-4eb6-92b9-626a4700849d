"""
MCP tools for Pharmaciaty Products API.
"""

from typing import List, Optional, Annotated
from pydantic import Field
from fastmcp import FastMCP

from ..api.client import PharmaciatyAPIClient
from ..api.models import ProductSearchResult, Product, InventoryInfo
from ..utils.logging import LoggerMixin


class ProductTools(LoggerMixin):
    """
    Collection of MCP tools for product operations.
    """
    
    def __init__(self, mcp: FastMCP, api_client: PharmaciatyAPIClient):
        self.mcp = mcp
        self.api_client = api_client
        self._register_tools()
    
    def _register_tools(self):
        """Register all product-related tools."""
        
        @self.mcp.tool
        async def search_products(
            search: Annotated[str, Field(
                description="Search term to find products (e.g., product name, brand, ingredient)",
                examples=["Paracetamol", "CISTAL", "diabetes medication"]
            )] = "",
            limit: Annotated[int, Field(
                description="Number of products to return (max: 100)",
                ge=1, le=100
            )] = 20,
            category: Annotated[Optional[str], Field(
                description="Product category filter (e.g., 'Pain Management', 'Children Health')"
            )] = None,
            is_available: Annotated[bool, Field(
                description="Filter for available products only"
            )] = True,
            warehouses: Annotated[Optional[str], Field(
                description="Comma-separated warehouse IDs to filter by"
            )] = None
        ) -> ProductSearchResult:
            """
            Search for products in the Pharmaciaty catalog.
            
            This tool allows you to search the product catalog by name, brand, or ingredients.
            Results include product details such as price, composition, and availability.
            You can filter by category, availability, and specific warehouses.
            
            Returns a structured result with products and metadata including total count
            and pagination information.
            """
            try:
                self.logger.info(
                    "Searching products",
                    search=search,
                    limit=limit,
                    category=category,
                    is_available=is_available
                )
                
                result = await self.api_client.search_products(
                    search=search,
                    limit=limit,
                    category=category,
                    is_available=is_available,
                    warehouses=warehouses
                )
                
                self.logger.info(
                    "Product search completed",
                    total_found=result.total_count,
                    returned=len(result.products)
                )
                
                return result
                
            except Exception as e:
                self.logger.error("Product search failed", error=str(e))
                raise Exception(f"Failed to search products: {str(e)}")
        
        @self.mcp.tool
        async def get_product_details(
            product_id: Annotated[str, Field(
                description="The unique identifier of the product",
                examples=["12345", "PROD_ABC123"]
            )]
        ) -> Product:
            """
            Get detailed information about a specific product.
            
            Fetches comprehensive product information including description, composition,
            price, categories, and availability status. Use this when you need complete
            details about a specific product.
            
            Returns detailed product information including all available metadata.
            """
            try:
                self.logger.info("Getting product details", product_id=product_id)
                
                product = await self.api_client.get_product_details(product_id)
                
                self.logger.info(
                    "Product details retrieved",
                    product_id=product_id,
                    product_name=product.name
                )
                
                return product
                
            except Exception as e:
                self.logger.error("Failed to get product details", product_id=product_id, error=str(e))
                raise Exception(f"Failed to get product details for {product_id}: {str(e)}")
        
        @self.mcp.tool
        async def check_product_inventory(
            product_id: Annotated[str, Field(
                description="The unique identifier of the product to check inventory for",
                examples=["12345", "PROD_ABC123"]
            )]
        ) -> List[InventoryInfo]:
            """
            Check the inventory status for a specific product across all warehouses.
            
            Get current stock levels and availability information for a product
            in various warehouses and cities. This is useful for verifying if a product
            is available in specific locations and checking quantities.
            
            Returns a list of inventory information for each warehouse where the product
            is available, including quantities and location details.
            """
            try:
                self.logger.info("Checking product inventory", product_id=product_id)
                
                inventory = await self.api_client.check_inventory(product_id)
                
                total_quantity = sum(item.quantity for item in inventory)
                self.logger.info(
                    "Inventory check completed",
                    product_id=product_id,
                    warehouses=len(inventory),
                    total_quantity=total_quantity
                )
                
                return inventory
                
            except Exception as e:
                self.logger.error("Inventory check failed", product_id=product_id, error=str(e))
                raise Exception(f"Failed to check inventory for product {product_id}: {str(e)}")
        
        @self.mcp.tool
        async def search_products_by_category(
            category: Annotated[str, Field(
                description="Product category to search for",
                examples=["Pain Management", "Children Health", "Skin Care", "Diabetes"]
            )],
            page: Annotated[int, Field(
                description="Page number for pagination (starts from 1)",
                ge=1
            )] = 1,
            limit: Annotated[int, Field(
                description="Number of products per page (max: 100)",
                ge=1, le=100
            )] = 20,
            is_available: Annotated[bool, Field(
                description="Filter for available products only"
            )] = True
        ) -> ProductSearchResult:
            """
            Search for products within a specific category.
            
            Find all products within a specified category such as 'Pain Management',
            'Children Health', 'Skin Care', etc. Results can be paginated and filtered
            by availability. This is useful when you need to browse products by
            therapeutic area or product type.
            
            Returns paginated results with products in the specified category.
            """
            try:
                self.logger.info(
                    "Searching products by category",
                    category=category,
                    page=page,
                    limit=limit,
                    is_available=is_available
                )
                
                result = await self.api_client.search_products(
                    category=category,
                    page=page,
                    limit=limit,
                    is_available=is_available
                )
                
                self.logger.info(
                    "Category search completed",
                    category=category,
                    total_found=result.total_count,
                    returned=len(result.products)
                )
                
                return result
                
            except Exception as e:
                self.logger.error("Category search failed", category=category, error=str(e))
                raise Exception(f"Failed to search products in category '{category}': {str(e)}")

        # Note: Resources are not implemented in this version to keep it simple
        # They can be added later if needed

