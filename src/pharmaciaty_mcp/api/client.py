"""
Robust API client for Pharmaciaty Products API.
"""

import asyncio
from typing import Optional, Dict, Any, List
import httpx
from ..config import config
from ..utils.logging import LoggerMixin
from .models import (
    Product, 
    ProductSearchResult, 
    InventoryInfo, 
    APIError, 
    AuthenticationError, 
    RateLimitError, 
    NotFoundError
)


class PharmaciatyAPIClient(LoggerMixin):
    """
    Production-grade API client for Pharmaciaty Products API.
    
    Features:
    - Automatic retry with exponential backoff
    - Proper error handling and custom exceptions
    - Request/response logging
    - Rate limiting respect
    - Connection pooling
    """
    
    def __init__(
        self, 
        api_token: Optional[str] = None,
        base_url: Optional[str] = None,
        timeout: Optional[int] = None
    ):
        self.api_token = api_token or config.api_token
        self.base_url = base_url or config.api_base_url
        self.timeout = timeout or config.api_timeout
        
        if not self.api_token:
            raise ValueError("API token is required")
        
        self._client: Optional[httpx.AsyncClient] = None
        self.logger.info("Initialized Pharmaciaty API client", base_url=self.base_url)
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_client(self) -> httpx.AsyncClient:
        """Ensure HTTP client is initialized."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=self.timeout,
                headers={
                    "Authorization": f"Bearer {self.api_token}",
                    "User-Agent": f"Pharmaciaty-MCP-Server/{config.server_version}",
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
        return self._client
    
    async def close(self):
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        retries: int = 3
    ) -> Dict[str, Any]:
        """
        Make an HTTP request with error handling and retries.
        """
        client = await self._ensure_client()
        url = endpoint if endpoint.startswith('http') else f"{self.base_url}{endpoint}"
        
        for attempt in range(retries + 1):
            try:
                self.logger.debug(
                    "Making API request",
                    method=method,
                    url=url,
                    params=params,
                    attempt=attempt + 1
                )
                
                response = await client.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_data
                )
                
                # Handle different status codes
                if response.status_code == 200:
                    data = response.json()
                    self.logger.debug("API request successful", status_code=response.status_code)
                    return data
                elif response.status_code == 401:
                    raise AuthenticationError("Invalid API token", response.status_code)
                elif response.status_code == 404:
                    raise NotFoundError("Resource not found", response.status_code)
                elif response.status_code == 429:
                    raise RateLimitError("Rate limit exceeded", response.status_code)
                else:
                    response.raise_for_status()
                    
            except httpx.HTTPStatusError as e:
                if attempt == retries:
                    self.logger.error(
                        "API request failed after retries",
                        status_code=e.response.status_code,
                        response_text=e.response.text
                    )
                    raise APIError(
                        f"API request failed: {e.response.status_code}",
                        e.response.status_code,
                        {"response_text": e.response.text}
                    )
                
                # Exponential backoff
                wait_time = 2 ** attempt
                self.logger.warning(
                    "API request failed, retrying",
                    attempt=attempt + 1,
                    wait_time=wait_time,
                    error=str(e)
                )
                await asyncio.sleep(wait_time)
                
            except httpx.RequestError as e:
                if attempt == retries:
                    self.logger.error("Network error after retries", error=str(e))
                    raise APIError(f"Network error: {str(e)}")
                
                wait_time = 2 ** attempt
                self.logger.warning(
                    "Network error, retrying",
                    attempt=attempt + 1,
                    wait_time=wait_time,
                    error=str(e)
                )
                await asyncio.sleep(wait_time)
        
        raise APIError("Max retries exceeded")
    
    async def search_products(
        self,
        search: Optional[str] = None,
        page: int = 1,
        limit: int = 20,
        category: Optional[str] = None,
        is_available: bool = True,
        warehouses: Optional[str] = None,
        sort_by: str = "is_active",
        sort_order: str = "asc"
    ) -> ProductSearchResult:
        """
        Search for products in the catalog.
        """
        params = {
            "page": page,
            "limit": min(limit, 100),  # Cap at 100
            "is_available": is_available,
            "sort_by": sort_by,
            "sort_order": sort_order
        }
        
        if search:
            params["search"] = search
        if category:
            params["category"] = category
        if warehouses:
            params["warehouses"] = warehouses
        
        try:
            data = await self._make_request("GET", "/products/", params=params)
            
            # Parse products
            products = []
            for item in data.get("data", []):
                try:
                    products.append(Product(**item))
                except Exception as e:
                    self.logger.warning("Failed to parse product", error=str(e), product_data=item)
            
            return ProductSearchResult(
                total_count=data.get("count", len(products)),
                page=page,
                limit=limit,
                products=products,
                search_query=search,
                filters={k: v for k, v in params.items() if k not in ["page", "limit"]}
            )
            
        except Exception as e:
            self.logger.error("Product search failed", error=str(e), params=params)
            raise
    
    async def get_product_details(self, product_id: str) -> Product:
        """
        Get detailed information about a specific product.
        """
        try:
            data = await self._make_request("GET", f"/products/{product_id}/")
            return Product(**data)
        except Exception as e:
            self.logger.error("Failed to get product details", product_id=product_id, error=str(e))
            raise
    
    async def check_inventory(self, product_id: str) -> List[InventoryInfo]:
        """
        Check inventory for a specific product.
        """
        try:
            data = await self._make_request("GET", f"/products/{product_id}/stock/")
            
            # Handle different response formats
            if isinstance(data, list):
                inventory_list = data
            elif isinstance(data, dict) and "data" in data:
                inventory_list = data["data"]
            else:
                inventory_list = [data]
            
            inventory = []
            for item in inventory_list:
                try:
                    inventory.append(InventoryInfo(product_id=product_id, **item))
                except Exception as e:
                    self.logger.warning("Failed to parse inventory", error=str(e), inventory_data=item)
            
            return inventory
            
        except Exception as e:
            self.logger.error("Inventory check failed", product_id=product_id, error=str(e))
            raise
