"""
Data models for Pharmaciaty API responses.
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator


class Product(BaseModel):
    """Product information model."""

    id: str = Field(description="Unique product identifier")
    name: str = Field(description="Product name")
    brand: Optional[str] = Field(default=None, description="Product brand")
    description: Optional[str] = Field(default=None, description="Product description")
    composition: Optional[str] = Field(default=None, description="Product composition/ingredients")
    category: Optional[str] = Field(default=None, description="Product category")
    price: Optional[float] = Field(default=None, description="Product price")
    currency: Optional[str] = Field(default=None, description="Price currency")
    is_available: bool = Field(default=True, description="Product availability status")
    is_active: bool = Field(default=True, description="Product active status")
    image_url: Optional[str] = Field(default=None, description="Product image URL")

    @field_validator('id', mode='before')
    @classmethod
    def convert_id_to_string(cls, v: Union[str, int]) -> str:
        """Convert product ID to string if it's an integer."""
        return str(v)

    class Config:
        extra = "allow"  # Allow additional fields from API


class ProductSearchResult(BaseModel):
    """Product search result model."""
    
    total_count: int = Field(description="Total number of products found")
    page: int = Field(description="Current page number")
    limit: int = Field(description="Number of products per page")
    products: List[Product] = Field(description="List of products")
    search_query: Optional[str] = Field(default=None, description="Search query used")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="Applied filters")


class InventoryInfo(BaseModel):
    """Inventory information model."""
    
    product_id: str = Field(description="Product identifier")
    warehouse_id: Optional[str] = Field(default=None, description="Warehouse identifier")
    warehouse_name: Optional[str] = Field(default=None, description="Warehouse name")
    quantity: int = Field(description="Available quantity")
    reserved_quantity: Optional[int] = Field(default=None, description="Reserved quantity")
    location: Optional[str] = Field(default=None, description="Warehouse location")
    last_updated: Optional[str] = Field(default=None, description="Last update timestamp")
    
    class Config:
        extra = "allow"


class APIError(Exception):
    """Custom exception for API errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class AuthenticationError(APIError):
    """Authentication-related API error."""
    pass


class RateLimitError(APIError):
    """Rate limit exceeded error."""
    pass


class NotFoundError(APIError):
    """Resource not found error."""
    pass
