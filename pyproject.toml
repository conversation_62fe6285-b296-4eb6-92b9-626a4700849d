[project]
name = "catelogue-mcp"
version = "0.1.0"
description = "A microservice for managing and searching pharmaceutical products."
authors = ["Aamir"]
license = "MIT"
dependencies = [
    "fastmcp",
    "uv",
    "httpx",
    "pydantic",
    "dataclasses-json",
]
dev-dependencies = [
    "pytest",
    "pytest-asyncio",
    "mypy",
    "black",
    "isort",
    "flake8",
    "bandit",
    "pre-commit",
    "mcp[cli]",
]
[tool.mcp]
name = "catelogue-mcp"
description = "A microservice for managing and searching pharmaceutical products."
version = "0.1.0"
entry-point = "main:app"
[tool.mcp.dependencies]
fastmcp = ">=0.1.0"
uv = ">=0.1.0"
httpx = ">=0.1.0"
pydantic = ">=1.8.2"
dataclasses-json = ">=0.5.6"
[tool.mcp.dev-dependencies]
pytest = ">=6.2.4"
pytest-asyncio = ">=0.14.0"
mypy = ">=0.910"
black = ">=21.7b0"
isort = ">=5.9.3"
flake8 = ">=3.9.2"
bandit = ">=1.7.0"
pre-commit = ">=2.13.0"
[tool.mcp.scripts]
run = "uv run --with mcp[cli] mcp run main.py"
[tool.mcp.settings]
transport = "stdio"
[tool.mcp.server]
host = "0.0.0.0"
port = 8000
[tool.mcp.logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"