#!/bin/zsh
# Deploy MCP Catalog server to Google Cloud Run

PROJECT_ID=""
REGION="YOUR_REGION"
SERVICE_NAME="mcp-catelog"

# Authenticate and set project
# gcloud auth login
# gcloud config set project $PROJECT_ID

gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME .
gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated

echo "Deployment complete. Visit Cloud Run console for service URL."
